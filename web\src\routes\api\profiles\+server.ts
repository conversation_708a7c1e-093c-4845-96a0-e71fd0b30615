import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types.js';
import { prisma } from '$lib/server/prisma';
import { hasFeatureAccess, getFeatureAccessDetails } from '$lib/server/feature-check';
import { incrementFeatureUsage } from '$lib/server/feature-usage';

// Using the shared Prisma client from $lib/server/prisma

// Handle GET request (fetching profiles and resumes)
export const GET: RequestHandler = async ({ locals }) => {
  const user = locals.user;
  if (!user) return new Response('Unauthorized', { status: 401 });

  // Fetch profiles associated with the user or the user's team
  const profiles = await prisma.profile.findMany({
    where: {
      OR: [
        { userId: user.id },
        {
          team: {
            members: {
              some: { userId: user.id },
            },
          },
        },
      ],
    },
    select: { id: true, name: true },
  });

  // Fetch resumes that are either unused (not linked to a profile) or used by the profile
  const resumes = await prisma.resume.findMany({
    where: {
      OR: [
        { profileId: null }, // Resumes not associated with any profile
        { usedByProfile: null }, // Resumes not used by any profile
      ],
    },
    select: {
      id: true,
      label: true,
      profileId: true, // Include profileId to check the association
      usedByProfile: true, // Include usedByProfileId to check if it's been used
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  return json({ profiles, resumes });
};

// Handle POST request (creating a new profile)
export const POST: RequestHandler = async ({ request, locals }) => {
  const user = locals.user;
  if (!user) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    console.log('Creating profile for user:', user.id);
    const body = await request.json();
    console.log('Request body:', body);

    const { name, data, defaultDocumentId } = body;

    if (!name) {
      console.log('Profile name is required');
      return new Response('Profile name is required', { status: 400 });
    }

    // Check if the user has access to the job_search_profiles feature
    // We'll log this for debugging but not enforce it
    const hasAccess = await hasFeatureAccess(user.id, 'job_search_profiles');
    console.log('User access to job_search_profiles:', hasAccess);

    // Get user role for logging purposes
    const userData = await prisma.user.findUnique({
      where: { id: user.id },
      select: { role: true },
    });

    console.log('User role:', userData?.role);
    console.log('Allowing user to create a profile regardless of feature check');

    // We'll skip the feature access check and rely only on the profile limit check below

    // Get the user's current profiles count
    const userProfilesCount = await prisma.profile.count({
      where: { userId: user.id },
    });

    // Try to get the profile limit from the user's plan first
    let profileLimit = 1; // Default fallback

    try {
      // Get feature access details from the user's plan
      const accessDetails = await getFeatureAccessDetails(user.id, 'job_search_profiles');
      console.log('Feature access details:', JSON.stringify(accessDetails));

      if (accessDetails.hasAccess && accessDetails.limits && accessDetails.limits.length > 0) {
        // Look for either 'job_search_profiles' or 'job_search_profiles_limit' as the limit ID
        const foundLimit = accessDetails.limits.find(
          (l) => l.limitId === 'job_search_profiles' || l.limitId === 'job_search_profiles_limit'
        );

        if (foundLimit) {
          if (foundLimit.value === 'unlimited') {
            profileLimit = Infinity;
            console.log('User has unlimited profiles from plan');
          } else {
            profileLimit = parseInt(foundLimit.value, 10);
            console.log(`User has ${profileLimit} profiles limit from plan`);
          }
        }
      }
    } catch (error) {
      console.error('Error getting feature access details:', error);
    }

    // If we couldn't get a limit from the plan, fall back to role-based limits
    if (profileLimit === 1) {
      try {
        const { Roles } = await import('$lib/config/roles');
        const roleConfig = Roles[userData.role as keyof typeof Roles] || Roles['free'];

        if (roleConfig.limits.profiles !== undefined) {
          profileLimit =
            roleConfig.limits.profiles === null ? Infinity : roleConfig.limits.profiles;
          console.log(`Using role-based profile limit: ${profileLimit} for role ${userData.role}`);
        }
      } catch (error) {
        console.error('Error getting role-based limits:', error);
      }
    }

    // Check if the user has reached their profile limit
    if (userProfilesCount >= profileLimit) {
      console.log(`User has reached their profile limit: ${userProfilesCount}/${profileLimit}`);

      return json(
        {
          success: false,
          error:
            profileLimit === 1
              ? `Your current plan allows only ${profileLimit} profile. Please upgrade your plan to create more profiles.`
              : `You've reached your profile limit of ${profileLimit}. Please upgrade your plan to create more profiles.`,
          limitReached: true,
          currentCount: userProfilesCount,
          limit: profileLimit,
        },
        { status: 403 }
      );
    }

    // Create the profile
    const newProfile = await prisma.profile.create({
      data: {
        name,
        userId: user.id,
        ...(defaultDocumentId && { defaultDocumentId }),
      },
    });

    // Then create profile data if provided
    if (data) {
      await prisma.profileData.create({
        data: {
          profileId: newProfile.id,
          data,
        },
      });
    }

    // Increment the usage counter for this feature
    await incrementFeatureUsage(user.id, 'job_search_profiles', 'job_search_profiles');

    console.log('Profile created successfully:', newProfile.id);

    return json({
      success: true,
      profileId: newProfile.id,
      profile: {
        id: newProfile.id,
        name: newProfile.name,
        createdAt: newProfile.createdAt,
        updatedAt: newProfile.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error creating profile:', error);
    return json(
      {
        success: false,
        error: 'Failed to create profile',
        details: error.message || String(error),
      },
      { status: 500 }
    );
  }
};
