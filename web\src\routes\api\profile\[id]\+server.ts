import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth.js';

// Using the shared Prisma client from $lib/server/prisma

export async function GET({ params, locals }) {
  const user = locals.user;
  if (!user) return new Response('Unauthorized', { status: 401 });

  const profileId = params.id;

  const profile = await prisma.profile.findUnique({
    where: { id: profileId },
    include: {
      data: true,
      team: {
        include: {
          members: true,
        },
      },
      documents: true,
    },
  });

  if (!profile) {
    return new Response('Profile not found', { status: 404 });
  }

  const isOwner = profile.userId === user.id;
  const isTeamMember = profile.team?.members?.some((m) => m.userId === user.id);

  if (!isOwner && !isTeamMember) {
    return new Response('Forbidden', { status: 403 });
  }

  return json(profile);
}

export async function DELETE({ params, cookies }) {
  // Get token from cookies
  const token = cookies.get('auth_token');
  if (!token) {
    return new Response('Unauthorized', { status: 401 });
  }

  const user = await verifySessionToken(token);
  if (!user) {
    return new Response('Unauthorized', { status: 401 });
  }

  const profileId = params.id;

  const profile = await prisma.profile.findUnique({
    where: { id: profileId },
    include: {
      team: {
        include: {
          members: true,
        },
      },
    },
  });

  if (!profile) {
    return new Response('Profile not found', { status: 404 });
  }

  const isOwner = profile.userId === user.id;
  const isTeamMember = profile.team?.members?.some((m) => m.userId === user.id);

  if (!isOwner && !isTeamMember) {
    return new Response('Forbidden', { status: 403 });
  }

  try {
    // Start a transaction to ensure all related data is deleted properly
    await prisma.$transaction(async (tx) => {
      // First, find documents related to this profile
      const documents = await tx.document.findMany({
        where: { profileId },
        select: { id: true },
      });

      // Get document IDs
      const documentIds = documents.map((doc) => doc.id);

      // Find resumes related to these documents
      const resumes = await tx.resume.findMany({
        where: { documentId: { in: documentIds } },
        select: { id: true },
      });

      // Get resume IDs
      const resumeIds = resumes.map((resume) => resume.id);

      // Delete ParsedResume records from workers schema (if they exist)
      if (resumeIds.length > 0) {
        try {
          // Use raw SQL to delete from workers schema since it's in a different schema
          await tx.$executeRaw`DELETE FROM "workers"."ParsedResume" WHERE "resumeId" = ANY(${resumeIds})`;
        } catch (parseError) {
          // ParsedResume might not exist or be accessible, continue with deletion
          console.warn('Could not delete ParsedResume records:', parseError);
        }
      }

      // Delete resumes related to these documents
      if (documentIds.length > 0) {
        await tx.resume.deleteMany({
          where: { documentId: { in: documentIds } },
        });
      }

      // Delete documents related to this profile
      await tx.document.deleteMany({
        where: { profileId },
      });

      // Delete automation runs related to this profile
      await tx.automationRun.deleteMany({
        where: { profileId },
      });

      // Delete job match analyses related to this profile
      await tx.jobMatchAnalysis.deleteMany({
        where: { profileId },
      });

      // Delete job searches related to this profile
      await tx.jobSearch.deleteMany({
        where: { profileId },
      });

      // Delete profile data (this should cascade automatically, but let's be explicit)
      await tx.profileData.deleteMany({
        where: { profileId },
      });

      // Finally, delete the profile itself
      await tx.profile.delete({
        where: { id: profileId },
      });
    });
  } catch (error) {
    console.error('Error deleting profile:', error);
    return json({ error: 'Failed to delete profile', details: error.message }, { status: 500 });
  }

  return json({ success: true });
}

export async function PATCH({ params, request, locals }) {
  const user = locals.user;
  if (!user) return new Response('Unauthorized', { status: 401 });

  const profileId = params.id;

  try {
    const updates = await request.json();
    console.log('Profile update request:', JSON.stringify(updates).substring(0, 200) + '...');

    // Only allow users to edit their own profiles
    const profile = await prisma.profile.findUnique({
      where: { id: profileId },
      include: { team: { include: { members: true } } },
    });

    if (!profile) {
      return json({ error: 'Profile not found' }, { status: 404 });
    }

    const isOwner = profile.userId === user.id;
    const isTeamMember = profile.team?.members.some((m) => m.userId === user.id);

    if (!isOwner && !isTeamMember) return new Response('Forbidden', { status: 403 });

    // Handle profile name update separately
    if (updates.name) {
      await prisma.profile.update({
        where: { id: profileId },
        data: {
          name: updates.name,
          updatedAt: new Date(),
        },
      });
    }

    // If there's no data to update, return success
    if (!updates.data && Object.keys(updates).length <= 1) {
      return json({ success: true, message: 'Profile updated successfully' });
    }

    // Upsert profile data
    let profileDataUpdates = {};

    // Handle legacy data format where updates.data is a JSON string
    if (updates.data && typeof updates.data === 'string') {
      try {
        const parsedData = JSON.parse(updates.data);
        console.log('Parsed profile data:', JSON.stringify(parsedData).substring(0, 200) + '...');

        // Extract fields from the parsed data
        profileDataUpdates = {
          data: updates.data, // Store the original JSON string
          fullName: parsedData.fullName || parsedData.personalInfo?.fullName || null,
          email: parsedData.email || parsedData.personalInfo?.email || null,
          phone: parsedData.phone || parsedData.personalInfo?.phone || null,
          address:
            parsedData.location ||
            parsedData.personalInfo?.location ||
            parsedData.personalInfo?.address ||
            null,
          summary: parsedData.summary || parsedData.personalInfo?.summary || null,

          // Handle structured data - store as JSON strings
          education: parsedData.education ? JSON.stringify(parsedData.education) : null,
          experience: parsedData.workExperience ? JSON.stringify(parsedData.workExperience) : null,
          skills: parsedData.skills
            ? JSON.stringify(parsedData.skills)
            : parsedData.skillsData?.list
              ? JSON.stringify(parsedData.skillsData.list)
              : null,
          languages: parsedData.languages ? JSON.stringify(parsedData.languages) : null,
          certifications: parsedData.certifications
            ? JSON.stringify(parsedData.certifications)
            : null,
          achievements: parsedData.achievements ? JSON.stringify(parsedData.achievements) : null,
        };
      } catch (error) {
        console.error('Error parsing profile data:', error);
        return json({ error: 'Invalid profile data format' }, { status: 400 });
      }
    } else if (updates.data && typeof updates.data === 'object') {
      // Handle direct object updates
      const data = updates.data;
      profileDataUpdates = {
        data: JSON.stringify(data), // Store the data as a JSON string
        fullName: data.fullName || data.personalInfo?.fullName || null,
        email: data.email || data.personalInfo?.email || null,
        phone: data.phone || data.personalInfo?.phone || null,
        address: data.location || data.personalInfo?.location || data.personalInfo?.address || null,
        summary: data.summary || data.personalInfo?.summary || null,

        // Handle structured data - store as JSON strings
        education: data.education ? JSON.stringify(data.education) : null,
        experience: data.workExperience ? JSON.stringify(data.workExperience) : null,
        skills: data.skills
          ? JSON.stringify(data.skills)
          : data.skillsData?.list
            ? JSON.stringify(data.skillsData.list)
            : null,
        languages: data.languages ? JSON.stringify(data.languages) : null,
        certifications: data.certifications ? JSON.stringify(data.certifications) : null,
        achievements: data.achievements ? JSON.stringify(data.achievements) : null,
      };
    } else {
      // Direct field updates
      profileDataUpdates = updates;
    }

    // Check if ProfileData exists
    const existingData = await prisma.profileData.findUnique({
      where: { profileId },
    });

    let updated;
    if (existingData) {
      // Update existing record
      updated = await prisma.profileData.update({
        where: { profileId },
        data: {
          ...profileDataUpdates,
          updatedAt: new Date(),
        },
      });
    } else {
      // Create new record
      updated = await prisma.profileData.create({
        data: {
          profileId,
          ...profileDataUpdates,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });
    }

    // Update the profile's updatedAt timestamp
    await prisma.profile.update({
      where: { id: profileId },
      data: { updatedAt: new Date() },
    });

    return json({ updated });
  } catch (error) {
    console.error('Error updating profile data:', error);
    return json(
      {
        error: 'Failed to update profile data',
        details: error.message || String(error),
      },
      { status: 500 }
    );
  }
}
