<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import * as AlertDialog from '$lib/components/ui/alert-dialog/index.js';
  import { Plus, FileText, Briefcase, Trash2, Edit, Calendar, User, Loader2 } from 'lucide-svelte';
  import SEO from '$components/shared/SEO.svelte';
  import { goto } from '$app/navigation';
  import { toast } from 'svelte-sonner';
  import {
    parseProfileData,
    migrateProfileData,
    calculateProfileCompletion,
  } from '$lib/utils/profileHelpers';

  const { data } = $props();

  // State for error message
  let errorMessage = $state('');
  let showLimitError = $state(false);
  let limitErrorMessage = $state('');

  // State for delete dialog
  let deleteDialogOpen = $state(false);
  let profileToDelete = $state<any>(null);
  let isDeleting = $state(false);

  // Create a new profile and navigate to it
  async function navigateToProfile() {
    try {
      // Reset error states
      errorMessage = '';
      showLimitError = false;
      limitErrorMessage = '';

      console.log('Creating new profile...');

      // Create a new profile in the database
      const response = await fetch('/api/profiles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'New Profile',
        }),
      });

      console.log('API response status:', response.status);

      // Parse the response as JSON first to handle both success and error cases
      const result = await response.json();
      console.log('API response data:', result);

      if (!response.ok) {
        // Check if this is a limit reached error
        if (response.status === 403 && result.limitReached) {
          console.log('Profile limit reached:', result.error);
          showLimitError = true;
          limitErrorMessage = result.error;
          return;
        }

        // Handle other errors
        errorMessage = result.error || 'Failed to create profile';
        throw new Error(errorMessage);
      }

      // Navigate to the new profile
      if (result.profileId) {
        console.log(`Navigating to profile: ${result.profileId}`);
        goto(`/dashboard/settings/profile/${result.profileId}`);
      } else {
        throw new Error('No profile ID returned');
      }
    } catch (error) {
      console.error('Error creating profile:', error);
      errorMessage = error instanceof Error ? error.message : 'Unknown error';
    }
  }

  // Format date
  function formatDate(dateString: string | Date): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  // Open delete dialog
  function openDeleteDialog(profile: any) {
    profileToDelete = profile;
    deleteDialogOpen = true;
  }

  // Delete profile
  async function deleteProfile() {
    if (!profileToDelete) return;

    try {
      isDeleting = true;

      const response = await fetch(`/api/profile/${profileToDelete.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        let errorMessage = 'Failed to delete profile';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          // If response is not JSON, use status text
          errorMessage = response.statusText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      toast.success('Profile deleted successfully');
      deleteDialogOpen = false;
      profileToDelete = null;

      // Reload the page after a short delay to allow the toast to be seen
      setTimeout(() => {
        window.location.reload();
      }, 800);
    } catch (error) {
      console.error('Error deleting profile:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete profile');
    } finally {
      isDeleting = false;
    }
  }
</script>

<SEO
  title="Profiles - Hirli"
  description="Create and manage your resume profiles for different job types and industries. Organize your job applications with customized profiles."
  keywords="resume profiles, job applications, career profiles, job search, resume management"
  url="https://hirli.com/dashboard/settings/profile" />

<div class="border-border flex flex-row justify-between border-b p-6">
  <div class="flex flex-col">
    <h2 class="text-lg font-semibold">Profiles</h2>
    <p class="text-muted-foreground">
      Create and manage your profiles for job applications, automations, and more.
    </p>
  </div>

  <div class="items-bottom flex">
    <Button class="p-4" variant="outline" onclick={navigateToProfile}>
      <Plus class="mr-2 h-4 w-4" />
      Create New Profile
    </Button>
  </div>
</div>
<div class="border-border flex items-center justify-between border-b p-4">
  <div class="flex flex-col">
    <h4 class="text-md font-semibold">My Profiles</h4>
    <p class="text-muted-foreground text-sm">
      Manage your resume profiles for different job types.
    </p>
  </div>
  {#if data.profileLimit}
    <div class="text-muted-foreground text-sm">
      <span class={data.profiles.length >= data.profileLimit ? 'text-destructive font-medium' : ''}>
        {data.profiles.length} / {data.profileLimit}
      </span> profiles used
    </div>
  {/if}
</div>

{#if errorMessage}
  <div class="bg-destructive/10 text-destructive mb-4 rounded-md p-4">
    <p>{errorMessage}</p>
  </div>
{/if}

{#if showLimitError}
  <div class="mb-4 rounded-md border border-amber-200 bg-amber-50 p-4 text-amber-800">
    <h3 class="mb-2 font-semibold">
      {limitErrorMessage.includes('does not include')
        ? 'Feature Not Available'
        : 'Profile Limit Reached'}
    </h3>
    <p class="mb-3">{limitErrorMessage}</p>
    <Button
      variant="outline"
      class="bg-amber-100 hover:bg-amber-200"
      onclick={() => goto('/dashboard/settings/billing')}>
      Upgrade Plan
    </Button>
  </div>
{/if}

<div class="p-4">
  {#if data.profiles && data.profiles.length > 0}
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {#each data.profiles as profile}
        {@const parsedData = profile.data?.data ? parseProfileData(profile.data.data) : {}}
        {@const profileData = migrateProfileData(parsedData)}
        <Card.Root class="hover:border-primary/50 overflow-hidden border-2 transition-all">
          <div class="p-6">
            <!-- Header with profile name and job type -->
            <div class="mb-3 flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <div class="bg-primary/10 text-primary rounded-full p-2">
                  <User class="h-5 w-5" />
                </div>
                <h3 class="text-lg font-semibold">{profile.name}</h3>
              </div>
              <div class="bg-primary/10 text-primary rounded-full px-3 py-1 text-xs font-medium">
                {profileData.jobType || 'General'}
              </div>
            </div>

            <!-- Profile completion progress -->
            {#if profileData}
              {@const completion = calculateProfileCompletion(profileData)}
              <div class="mb-4">
                <div class="mb-1 flex justify-between text-xs">
                  <span>Profile Completion</span>
                  <span class="font-medium">{completion}%</span>
                </div>
                <div class="bg-muted h-2 w-full rounded-full">
                  <div class="bg-primary h-2 rounded-full" style="width: {completion}%"></div>
                </div>
              </div>
            {/if}

            <!-- Key profile information -->
            <div class="mb-4 space-y-2">
              <!-- Personal info -->
              {#if profileData.personalInfo?.fullName || profileData.fullName}
                <div class="flex items-start space-x-2">
                  <User class="text-muted-foreground mt-0.5 h-4 w-4" />
                  <div>
                    <p class="text-sm font-medium">
                      {profileData.personalInfo?.fullName || profileData.fullName}
                    </p>
                    {#if profileData.personalInfo?.location || profileData.location}
                      <p class="text-muted-foreground text-xs">
                        {profileData.personalInfo?.location || profileData.location}
                      </p>
                    {/if}
                  </div>
                </div>
              {/if}

              <!-- Industry -->
              {#if profileData.industry}
                <div class="flex items-center space-x-2">
                  <Briefcase class="text-muted-foreground h-4 w-4" />
                  <span class="text-sm">{profileData.industry}</span>
                </div>
              {/if}

              <!-- Skills (show up to 3) -->
              {#if profileData.skillsData?.list && profileData.skillsData.list.length > 0}
                <div class="mt-2 flex flex-wrap gap-1">
                  {#each profileData.skillsData.list.slice(0, 3) as skill}
                    <Badge variant="outline" class="bg-primary/5 text-xs">{skill}</Badge>
                  {/each}
                  {#if profileData.skillsData.list.length > 3}
                    <Badge variant="outline" class="bg-muted/50 text-xs"
                      >+{profileData.skillsData.list.length - 3} more</Badge>
                  {/if}
                </div>
              {/if}

              <!-- Resume -->
              {#if profile.defaultDocument}
                <div class="mt-1 flex items-center space-x-2">
                  <FileText class="text-muted-foreground h-4 w-4" />
                  <span class="text-sm">Resume: {profile.defaultDocument.label}</span>
                </div>
              {/if}
            </div>

            <!-- Job preferences summary -->
            {#if profileData.jobPreferences}
              <div class="mt-3 border-t pt-3">
                <h4 class="mb-2 text-xs font-medium">Job Preferences</h4>
                <div class="flex flex-wrap gap-2">
                  {#if profileData.jobPreferences.jobSearchStatus}
                    <Badge variant="outline" class="bg-primary/5 text-xs"
                      >{profileData.jobPreferences.jobSearchStatus}</Badge>
                  {/if}
                  {#if profileData.jobPreferences.remotePreference}
                    <Badge variant="outline" class="bg-primary/5 text-xs"
                      >{profileData.jobPreferences.remotePreference}</Badge>
                  {/if}
                  {#if profileData.jobPreferences.minimumSalary}
                    <Badge variant="outline" class="bg-primary/5 text-xs"
                      >{profileData.jobPreferences.minimumSalary}</Badge>
                  {/if}
                </div>
              </div>
            {/if}

            <!-- Last updated -->
            <div class="text-muted-foreground mt-4 text-xs">
              <div class="flex items-center space-x-1">
                <Calendar class="h-3.5 w-3.5" />
                <span>Last updated: {formatDate(profile.updatedAt)}</span>
              </div>
            </div>
          </div>

          <!-- Action buttons -->
          <div class="bg-muted/50 flex border-t">
            <Button
              variant="ghost"
              class="flex-1 rounded-none"
              onclick={() => goto(`/dashboard/settings/profile/${profile.id}`)}>
              <Edit class="mr-2 h-4 w-4" />
              Edit
            </Button>
            <div class="border-border border-r"></div>
            <Button
              variant="ghost"
              class="text-destructive hover:text-destructive hover:bg-destructive/10 flex-1 rounded-none"
              onclick={() => openDeleteDialog(profile)}>
              <Trash2 class="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        </Card.Root>
      {/each}
    </div>
  {:else}
    <div
      class="border-border flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
      <FileText class="text-muted-foreground mb-4 h-12 w-12" />
      <h3 class="mb-2 text-lg font-semibold">No profiles yet</h3>
      <p class="text-muted-foreground mb-4 text-sm">
        Create your first resume profile to start applying for jobs.
      </p>
      <Button onclick={navigateToProfile}>
        <Plus class="mr-2 h-4 w-4" />
        Create Profile
      </Button>
    </div>
  {/if}
</div>

<!-- Delete Confirmation Dialog -->
<AlertDialog.Root bind:open={deleteDialogOpen}>
  <AlertDialog.Content>
    <AlertDialog.Header>
      <AlertDialog.Title>Delete Profile</AlertDialog.Title>
      <AlertDialog.Description>
        Are you sure you want to delete the profile "{profileToDelete?.name}"? This action cannot be
        undone.
      </AlertDialog.Description>
    </AlertDialog.Header>
    <AlertDialog.Footer>
      <AlertDialog.Cancel onclick={() => (deleteDialogOpen = false)}>Cancel</AlertDialog.Cancel>
      <AlertDialog.Action
        onclick={deleteProfile}
        disabled={isDeleting}
        class={isDeleting
          ? 'cursor-not-allowed opacity-70'
          : 'bg-destructive text-destructive-foreground hover:bg-destructive/90'}>
        {#if isDeleting}
          <Loader2 class="mr-2 h-4 w-4 animate-spin" />
          Deleting...
        {:else}
          Delete
        {/if}
      </AlertDialog.Action>
    </AlertDialog.Footer>
  </AlertDialog.Content>
</AlertDialog.Root>
