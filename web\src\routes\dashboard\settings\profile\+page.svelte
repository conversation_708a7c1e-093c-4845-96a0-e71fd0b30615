<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { Input } from '$lib/components/ui/input/index.js';

  import * as AlertDialog from '$lib/components/ui/alert-dialog/index.js';
  import {
    Plus,
    FileText,
    Briefcase,
    Trash2,
    Edit,
    User,
    Loader2,
    Search,
    Users,
    MapPin,
    Star,
    Clock,
    ChevronLeft,
    ChevronRight,
  } from 'lucide-svelte';
  import SEO from '$components/shared/SEO.svelte';
  import { goto } from '$app/navigation';
  import { toast } from 'svelte-sonner';

  import {
    parseProfileData,
    migrateProfileData,
    calculateProfileCompletion,
  } from '$lib/utils/profileHelpers';
  import Progress from '$lib/components/ui/progress/progress.svelte';

  const { data } = $props();

  // State for error message
  let errorMessage = $state('');
  let showLimitError = $state(false);
  let limitErrorMessage = $state('');

  // State for delete dialog
  let deleteDialogOpen = $state(false);
  let profileToDelete = $state<any>(null);
  let isDeleting = $state(false);

  // Filter state
  let searchQuery = $state(data.filters.search);
  let selectedJobType = $state(data.filters.jobType);
  let selectedIndustry = $state(data.filters.industry);
  let selectedOwner = $state(data.filters.owner);
  let isLoading = $state(false);

  // Debounced search
  let searchTimeout: NodeJS.Timeout;

  // Apply filters function
  function applyFilters() {
    if (isLoading) return;

    isLoading = true;
    const params = new URLSearchParams();

    if (searchQuery) params.set('search', searchQuery);
    if (selectedJobType) params.set('jobType', selectedJobType);
    if (selectedIndustry) params.set('industry', selectedIndustry);
    if (selectedOwner && selectedOwner !== 'all') params.set('owner', selectedOwner);
    params.set('page', '1'); // Reset to first page when filtering

    const url = params.toString() ? `?${params.toString()}` : '';
    goto(url, { replaceState: true }).finally(() => {
      isLoading = false;
    });
  }

  // Debounced search handler
  function handleSearchInput() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      applyFilters();
    }, 300);
  }

  // Pagination handlers
  function goToPage(page: number) {
    if (isLoading) return;

    const params = new URLSearchParams(window.location.search);
    params.set('page', page.toString());
    goto(`?${params.toString()}`, { replaceState: true });
  }

  // Create a new profile and navigate to it
  async function navigateToProfile() {
    try {
      // Reset error states
      errorMessage = '';
      showLimitError = false;
      limitErrorMessage = '';

      console.log('Creating new profile...');

      // Create a new profile in the database
      const response = await fetch('/api/profiles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'New Profile',
        }),
      });

      console.log('API response status:', response.status);

      // Parse the response as JSON first to handle both success and error cases
      const result = await response.json();
      console.log('API response data:', result);

      if (!response.ok) {
        // Check if this is a limit reached error
        if (response.status === 403 && result.limitReached) {
          console.log('Profile limit reached:', result.error);
          showLimitError = true;
          limitErrorMessage = result.error;
          return;
        }

        // Handle other errors
        errorMessage = result.error || 'Failed to create profile';
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }

      // Navigate to the new profile
      if (result.profileId) {
        console.log(`Navigating to profile: ${result.profileId}`);
        goto(`/dashboard/settings/profile/${result.profileId}`);
      } else {
        errorMessage = result.error || 'No profile ID returned';
        toast.error(errorMessage);

        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Error creating profile:', error);
      errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(errorMessage);
    }
  }

  // Format date
  function formatDate(dateString: string | Date): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  // Open delete dialog
  function openDeleteDialog(profile: any) {
    profileToDelete = profile;
    deleteDialogOpen = true;
  }

  // Delete profile
  async function deleteProfile() {
    if (!profileToDelete) return;

    try {
      isDeleting = true;

      const response = await fetch(`/api/profile/${profileToDelete.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        let errorMessage = 'Failed to delete profile';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          // If response is not JSON, use status text
          errorMessage = response.statusText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      toast.success('Profile deleted successfully');
      deleteDialogOpen = false;
      profileToDelete = null;

      // Reload the page after a short delay to allow the toast to be seen
      setTimeout(() => {
        window.location.reload();
      }, 800);
    } catch (error) {
      console.error('Error deleting profile:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete profile');
    } finally {
      isDeleting = false;
    }
  }
</script>

<SEO
  title="Profiles - Hirli"
  description="Create and manage your resume profiles for different job types and industries. Organize your job applications with customized profiles."
  keywords="resume profiles, job applications, career profiles, job search, resume management"
  url="https://hirli.com/dashboard/settings/profile" />

<!-- Header with Create Profile Button -->
<div class="border-border flex flex-col gap-6 border-b p-6">
  <div class="flex items-center justify-between">
    <div>
      <h2 class="text-2xl font-bold">Profiles</h2>
      <p class="text-muted-foreground">
        Create and manage your profiles for job applications, automations, and more.
      </p>
    </div>
    <Button onclick={navigateToProfile} class="gap-2">
      <Plus class="h-4 w-4" />
      Create New Profile
    </Button>
  </div>

  <!-- Filters Section -->
  <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
    <div class="flex flex-1 gap-3">
      <!-- Search Input -->
      <div class="relative max-w-sm flex-1">
        <Search class="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
        <Input
          placeholder="Search profiles..."
          bind:value={searchQuery}
          oninput={handleSearchInput}
          class="pl-9" />
      </div>

      <!-- Job Type Filter -->
      {#if data.filters.jobTypes.length > 0}
        <select
          bind:value={selectedJobType}
          onchange={applyFilters}
          class="border-input bg-background ring-offset-background h-9 w-[180px] rounded-md border px-3 py-1 text-sm">
          <option value="">All Job Types</option>
          {#each data.filters.jobTypes as jobType}
            <option value={jobType}>{jobType}</option>
          {/each}
        </select>
      {/if}

      <!-- Industry Filter -->
      {#if data.filters.industries.length > 0}
        <select
          bind:value={selectedIndustry}
          onchange={applyFilters}
          class="border-input bg-background ring-offset-background h-9 w-[180px] rounded-md border px-3 py-1 text-sm">
          <option value="">All Industries</option>
          {#each data.filters.industries as industry}
            <option value={industry}>{industry}</option>
          {/each}
        </select>
      {/if}

      <!-- Owner Filter (only show if user has team access) -->
      {#if data.hasTeamAccess}
        <select
          bind:value={selectedOwner}
          onchange={applyFilters}
          class="border-input bg-background ring-offset-background h-9 w-[150px] rounded-md border px-3 py-1 text-sm">
          <option value="all">All Profiles</option>
          <option value="user">My Profiles</option>
          <option value="team">Team Profiles</option>
        </select>
      {/if}
    </div>

    <!-- Results Count -->
    <div class="text-muted-foreground text-sm">
      {data.pagination.totalProfiles} profile{data.pagination.totalProfiles !== 1 ? 's' : ''} found
    </div>
  </div>
</div>

<div class="p-6">

  {#if showLimitError}
    <div class="mb-4 rounded-md border border-amber-200 bg-amber-50 p-4 text-amber-800">
      <h3 class="mb-2 font-semibold">
        {limitErrorMessage.includes('does not include')
          ? 'Feature Not Available'
          : 'Profile Limit Reached'}
      </h3>
      <p class="mb-3">{limitErrorMessage}</p>
      <Button
        variant="outline"
        class="bg-amber-100 hover:bg-amber-200"
        onclick={() => goto('/dashboard/settings/billing')}>
        Upgrade Plan
      </Button>
    </div>
  {/if}

  <!-- Main Content -->
  {#if data.profiles && data.profiles.length > 0}
    <div class="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
      {#each data.profiles as profile}
        {@const parsedData = profile.data?.data ? parseProfileData(profile.data.data) : {}}
        {@const profileData = migrateProfileData(parsedData)}
        {@const completion = calculateProfileCompletion(profileData)}
        {@const isTeamProfile = profile.team && profile.userId !== data.user.id}

        <Card.Root class="group relative gap-0 overflow-hidden p-0 transition-all hover:shadow-md">
          <!-- Profile Header -->
          <Card.Header class="border-border flex items-start justify-between border-b !p-4">
            <div class="flex items-center gap-3">
              <div
                class="bg-primary/20 text-primary flex h-12 w-12 items-center justify-center rounded-full">
                {#if isTeamProfile}
                  <Users class="h-6 w-6" />
                {:else}
                  <User class="h-6 w-6" />
                {/if}
              </div>
              <div>
                <h3 class="text-lg font-semibold leading-tight">{profile.name}</h3>
                {#if isTeamProfile}
                  <p class="text-muted-foreground text-sm">Team: {profile.team.name}</p>
                {:else}
                  <p class="text-muted-foreground text-sm">Personal Profile</p>
                {/if}
              </div>
            </div>

            <!-- Job Type Badge -->
            {#if profileData.jobType}
              <Badge variant="secondary" class="bg-primary/10 text-primary border-primary/20">
                {profileData.jobType}
              </Badge>
            {/if}
          </Card.Header>

          <Card.Content class="p-0">
            <Progress value={completion} class="h-2 w-full rounded-none" />
            <!-- Profile Completion -->
            <div class="mt-1 flex items-center justify-between px-4 text-sm">
              <span class="text-muted-foreground">Profile Completion</span>
              <span class="font-medium">{completion}%</span>
            </div>

            <!-- Profile Content -->
            <div class="p-4">
              <!-- Key Information Grid -->
              <div class="mb-4 space-y-3">
                <!-- Personal Info -->
                {#if profileData.personalInfo?.fullName || profileData.fullName}
                  <div class="flex items-center gap-3">
                    <div class="bg-muted flex h-8 w-8 items-center justify-center rounded-full">
                      <User class="h-4 w-4" />
                    </div>
                    <div class="flex-1">
                      <p class="font-medium">
                        {profileData.personalInfo?.fullName || profileData.fullName}
                      </p>
                      {#if profileData.personalInfo?.location || profileData.location}
                        <p class="text-muted-foreground flex items-center gap-1 text-sm">
                          <MapPin class="h-3 w-3" />
                          {profileData.personalInfo?.location || profileData.location}
                        </p>
                      {/if}
                    </div>
                  </div>
                {/if}

                <!-- Industry -->
                {#if profileData.industry}
                  <div class="flex items-center gap-3">
                    <div class="bg-muted flex h-8 w-8 items-center justify-center rounded-full">
                      <Briefcase class="h-4 w-4" />
                    </div>
                    <span class="font-medium">{profileData.industry}</span>
                  </div>
                {/if}

                <!-- Resume -->
                {#if profile.defaultDocument}
                  <div class="flex items-center gap-3">
                    <div class="bg-muted flex h-8 w-8 items-center justify-center rounded-full">
                      <FileText class="h-4 w-4" />
                    </div>
                    <span class="font-medium">{profile.defaultDocument.label}</span>
                  </div>
                {/if}
              </div>

              <!-- Skills -->
              {#if profileData.skillsData?.list && profileData.skillsData.list.length > 0}
                <div class="mb-4">
                  <h4 class="text-muted-foreground mb-2 text-sm font-medium">Top Skills</h4>
                  <div class="flex flex-wrap gap-1">
                    {#each profileData.skillsData.list.slice(0, 4) as skill}
                      <Badge variant="outline" class="bg-primary/5 text-xs">{skill}</Badge>
                    {/each}
                    {#if profileData.skillsData.list.length > 4}
                      <Badge variant="outline" class="bg-muted text-xs">
                        +{profileData.skillsData.list.length - 4}
                      </Badge>
                    {/if}
                  </div>
                </div>
              {/if}

              <!-- Job Preferences -->
              {#if profileData.jobPreferences}
                <div class="mb-4">
                  <h4 class="text-muted-foreground mb-2 text-sm font-medium">Preferences</h4>
                  <div class="flex flex-wrap gap-1">
                    {#if profileData.jobPreferences.jobSearchStatus}
                      <Badge variant="outline" class="text-xs"
                        >{profileData.jobPreferences.jobSearchStatus}</Badge>
                    {/if}
                    {#if profileData.jobPreferences.remotePreference}
                      <Badge variant="outline" class="text-xs"
                        >{profileData.jobPreferences.remotePreference}</Badge>
                    {/if}
                  </div>
                </div>
              {/if}

              <!-- Footer -->
              <div class="border-muted flex items-center justify-between border-t pt-4">
                <div class="text-muted-foreground flex items-center gap-1 text-xs">
                  <Clock class="h-3 w-3" />
                  {formatDate(profile.updatedAt)}
                </div>
                <div class="flex items-center gap-1">
                  <Star class="text-primary h-4 w-4 fill-current" />
                  <span class="text-sm font-medium">{completion}%</span>
                </div>
              </div>
            </div>
          </Card.Content>
          <!-- Action buttons -->
          <Card.Footer class="bg-muted/50 flex gap-0 border-t !p-0">
            <Button
              variant="ghost"
              class="flex-1 rounded-none"
              onclick={() => goto(`/dashboard/settings/profile/${profile.id}`)}>
              <Edit class="mr-2 h-4 w-4" />
              Edit
            </Button>
            <div class="border-border border-r"></div>
            <Button
              variant="ghost"
              class="text-destructive hover:text-destructive hover:bg-destructive/10 flex-1 rounded-none"
              onclick={() => openDeleteDialog(profile)}>
              <Trash2 class="mr-2 h-4 w-4" />
              Delete
            </Button>
          </Card.Footer>
        </Card.Root>
      {/each}
    </div>

    <!-- Pagination -->
    {#if data.pagination.totalPages > 1}
      <div class="mt-8 flex items-center justify-center gap-2">
        <Button
          variant="outline"
          size="sm"
          disabled={!data.pagination.hasPrevPage || isLoading}
          onclick={() => goToPage(data.pagination.page - 1)}
          class="gap-1">
          <ChevronLeft class="h-4 w-4" />
          Previous
        </Button>

        <div class="flex items-center gap-1">
          {#each Array(Math.min(data.pagination.totalPages, 7)) as _, i}
            {@const pageNum =
              data.pagination.totalPages <= 7
                ? i + 1
                : data.pagination.page <= 4
                  ? i + 1
                  : data.pagination.page >= data.pagination.totalPages - 3
                    ? data.pagination.totalPages - 6 + i
                    : data.pagination.page - 3 + i}

            {#if pageNum >= 1 && pageNum <= data.pagination.totalPages}
              <Button
                variant={data.pagination.page === pageNum ? 'default' : 'outline'}
                size="sm"
                onclick={() => goToPage(pageNum)}
                disabled={isLoading}
                class="h-8 w-8 p-0">
                {pageNum}
              </Button>
            {/if}
          {/each}
        </div>

        <Button
          variant="outline"
          size="sm"
          disabled={!data.pagination.hasNextPage || isLoading}
          onclick={() => goToPage(data.pagination.page + 1)}
          class="gap-1">
          Next
          <ChevronRight class="h-4 w-4" />
        </Button>
      </div>
    {/if}
  {:else}
    <div
      class="border-border flex flex-col items-center justify-center rounded-lg border border-dashed p-12 text-center">
      <FileText class="text-muted-foreground mb-4 h-16 w-16" />
      <h3 class="mb-2 text-xl font-semibold">No profiles found</h3>
      <p class="text-muted-foreground mb-6 max-w-md text-sm">
        {#if searchQuery || selectedJobType || selectedIndustry}
          No profiles match your current filters. Try adjusting your search criteria.
        {:else}
          Create your first resume profile to start applying for jobs and managing your
          applications.
        {/if}
      </p>
      <Button onclick={navigateToProfile} class="gap-2">
        <Plus class="h-4 w-4" />
        Create New Profile
      </Button>
    </div>
  {/if}
</div>

<!-- Delete Confirmation Dialog -->
<AlertDialog.Root bind:open={deleteDialogOpen}>
  <AlertDialog.Content class="gap-0 p-0 sm:max-w-[425px]">
    <AlertDialog.Header>
      <AlertDialog.Title class="border-border border-b p-2">Delete Profile</AlertDialog.Title>
    </AlertDialog.Header>
    <AlertDialog.Description class="p-2">
      Are you sure you want to delete the profile "{profileToDelete?.name}"? This action cannot be
      undone.
    </AlertDialog.Description>

    <AlertDialog.Footer class="border-border border-t p-2">
      <AlertDialog.Cancel onclick={() => (deleteDialogOpen = false)}>Cancel</AlertDialog.Cancel>
      <AlertDialog.Action
        onclick={deleteProfile}
        disabled={isDeleting}
        class={isDeleting
          ? 'cursor-not-allowed opacity-70'
          : 'bg-destructive text-destructive-foreground hover:bg-destructive/90'}>
        {#if isDeleting}
          <Loader2 class="mr-2 h-4 w-4 animate-spin" />
          Deleting...
        {:else}
          Delete
        {/if}
      </AlertDialog.Action>
    </AlertDialog.Footer>
  </AlertDialog.Content>
</AlertDialog.Root>
